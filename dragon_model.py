"""
板凳龙数学模型
包含龙队结构定义、运动学计算和约束求解
"""

import numpy as np
import math
from typing import List, Tuple, Optional

class Handle:
    """把手类，表示板凳上的一个把手"""
    def __init__(self, x: float = 0.0, y: float = 0.0, vx: float = 0.0, vy: float = 0.0):
        self.x = x      # x坐标 (m)
        self.y = y      # y坐标 (m) 
        self.vx = vx    # x方向速度 (m/s)
        self.vy = vy    # y方向速度 (m/s)
    
    def position(self) -> Tuple[float, float]:
        return (self.x, self.y)
    
    def velocity(self) -> Tuple[float, float]:
        return (self.vx, self.vy)
    
    def speed(self) -> float:
        return math.sqrt(self.vx**2 + self.vy**2)

class Bench:
    """板凳类，表示龙队中的一节板凳"""
    def __init__(self, length: float, is_head: bool = False, is_tail: bool = False):
        self.length = length  # 板凳长度 (m)
        self.is_head = is_head
        self.is_tail = is_tail
        self.front_handle = Handle()  # 前把手
        self.rear_handle = Handle() if not is_tail else None  # 后把手（龙尾没有后把手）
        
        # 把手距离板头的距离 (m)
        self.handle_offset = 0.275  # 27.5cm
    
    def get_connection_length(self) -> float:
        """获取与下一节板凳的连接长度"""
        return self.length - 2 * self.handle_offset

class DragonModel:
    """板凳龙模型"""
    def __init__(self):
        self.benches: List[Bench] = []
        self.total_handles = 0
        self._initialize_dragon()
    
    def _initialize_dragon(self):
        """初始化龙队结构"""
        # 龙头 (341cm = 3.41m)
        head = Bench(3.41, is_head=True)
        self.benches.append(head)
        
        # 龙身 (221节，每节220cm = 2.20m)
        for i in range(221):
            body = Bench(2.20)
            self.benches.append(body)
        
        # 龙尾 (220cm = 2.20m)
        tail = Bench(2.20, is_tail=True)
        self.benches.append(tail)
        
        # 计算总把手数：223个前把手 + 1个龙尾后把手 = 224个
        self.total_handles = 224
    
    def get_connection_lengths(self) -> List[float]:
        """获取所有连接长度"""
        lengths = []
        for bench in self.benches[:-1]:  # 除了最后一节
            lengths.append(bench.get_connection_length())
        return lengths
    
    def get_all_handles(self) -> List[Handle]:
        """获取所有把手的列表"""
        handles = []
        for bench in self.benches:
            handles.append(bench.front_handle)
        # 添加龙尾的后把手
        if self.benches[-1].rear_handle:
            handles.append(self.benches[-1].rear_handle)
        return handles
    
    def update_positions(self, positions: List[Tuple[float, float]], 
                        velocities: List[Tuple[float, float]]):
        """更新所有把手的位置和速度"""
        handles = self.get_all_handles()
        for i, (handle, pos, vel) in enumerate(zip(handles, positions, velocities)):
            handle.x, handle.y = pos
            handle.vx, handle.vy = vel

class SpiralMotion:
    """螺线运动计算"""
    def __init__(self, pitch: float, initial_radius: float = 0.0, initial_angle: float = 0.0):
        self.pitch = pitch  # 螺距 (m)
        self.b = pitch / (2 * math.pi)  # 螺线参数
        self.r0 = initial_radius  # 初始半径
        self.theta0 = initial_angle  # 初始角度
        self.speed = 1.0  # 龙头前把手速度 (m/s)
    
    def radius(self, theta: float) -> float:
        """计算给定角度的半径"""
        return self.r0 + self.b * (theta - self.theta0)
    
    def position(self, theta: float) -> Tuple[float, float]:
        """计算给定角度的位置"""
        r = self.radius(theta)
        x = r * math.cos(theta)
        y = r * math.sin(theta)
        return (x, y)
    
    def theta_from_time(self, t: float) -> float:
        """从时间计算角度（使用数值积分）"""
        if t == 0:
            return self.theta0

        # 使用四阶龙格-库塔方法进行数值积分
        dt = 0.01  # 时间步长
        theta = self.theta0

        n_steps = int(t / dt)
        for i in range(n_steps):
            # RK4方法
            k1 = self._dtheta_dt(theta)
            k2 = self._dtheta_dt(theta + 0.5 * dt * k1)
            k3 = self._dtheta_dt(theta + 0.5 * dt * k2)
            k4 = self._dtheta_dt(theta + dt * k3)

            theta += dt * (k1 + 2*k2 + 2*k3 + k4) / 6

        # 处理剩余的时间
        remaining_time = t - n_steps * dt
        if remaining_time > 0:
            theta += remaining_time * self._dtheta_dt(theta)

        return theta

    def _dtheta_dt(self, theta: float) -> float:
        """计算角速度 dθ/dt"""
        r = self.radius(theta)
        # 速度大小 = sqrt((dr/dt)^2 + (r*dtheta/dt)^2) = dtheta/dt * sqrt(b^2 + r^2)
        # 因此 dtheta/dt = speed / sqrt(b^2 + r^2)
        return self.speed / math.sqrt(self.b**2 + r**2)
    
    def velocity(self, theta: float) -> Tuple[float, float]:
        """计算给定角度的速度"""
        r = self.radius(theta)
        # 计算角速度
        dtheta_dt = self.speed / math.sqrt(self.b**2 + r**2)
        
        # 速度分量
        vx = -r * math.sin(theta) * dtheta_dt + self.b * math.cos(theta) * dtheta_dt
        vy = r * math.cos(theta) * dtheta_dt + self.b * math.sin(theta) * dtheta_dt
        
        return (vx, vy)

def solve_constraint_position(prev_pos: Tuple[float, float], 
                            constraint_length: float,
                            prev_direction: Optional[Tuple[float, float]] = None) -> Tuple[float, float]:
    """
    求解约束位置
    给定前一个把手位置和连接长度，求解下一个把手位置
    """
    x1, y1 = prev_pos
    
    if prev_direction is None:
        # 如果没有方向信息，假设沿当前方向继续
        # 这里需要更复杂的逻辑来确定正确的方向
        # 简化处理：假设向内螺旋
        angle = math.atan2(y1, x1) + 0.1  # 稍微向内偏转
    else:
        # 使用前一个方向作为参考
        angle = math.atan2(prev_direction[1], prev_direction[0])
    
    # 计算下一个位置
    x2 = x1 + constraint_length * math.cos(angle)
    y2 = y1 + constraint_length * math.sin(angle)
    
    return (x2, y2)
