"""
简单的结果可视化脚本
生成基本的轨迹图和分析图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import math

def plot_basic_trajectory():
    """绘制基本轨迹图"""
    try:
        df = pd.read_excel('result1.xlsx')
        print(f"加载数据成功，形状: {df.shape}")
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：龙头轨迹
        times = [0, 60, 120, 180, 240, 300]
        colors = ['red', 'orange', 'yellow', 'green', 'blue', 'purple']
        
        for i, t in enumerate(times):
            if t <= df['time'].max():
                row = df[df['time'] == t].iloc[0]
                x_head = row['handle_1_x']
                y_head = row['handle_1_y']
                ax1.plot(x_head, y_head, 'o', color=colors[i], 
                        markersize=8, label=f't={t}s')
        
        # 绘制完整龙头轨迹
        x_trajectory = df['handle_1_x'].values
        y_trajectory = df['handle_1_y'].values
        ax1.plot(x_trajectory, y_trajectory, 'k-', alpha=0.3, linewidth=1)
        
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_title('龙头运动轨迹')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # 右图：速度分析
        times_all = df['time'].values
        vx = df['handle_1_vx'].values
        vy = df['handle_1_vy'].values
        speeds = np.sqrt(vx**2 + vy**2)
        
        ax2.plot(times_all, speeds, 'r-', linewidth=2, label='龙头速度')
        ax2.axhline(y=1.0, color='k', linestyle='--', alpha=0.5, label='目标速度1m/s')
        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('速度 (m/s)')
        ax2.set_title('龙头速度变化')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('basic_analysis.png', dpi=300, bbox_inches='tight')
        print("图表已保存为 basic_analysis.png")
        
        # 输出统计信息
        print(f"\n=== 统计分析 ===")
        print(f"平均速度: {np.mean(speeds):.6f} m/s")
        print(f"速度标准差: {np.std(speeds):.6f} m/s")
        print(f"最大速度: {np.max(speeds):.6f} m/s")
        print(f"最小速度: {np.min(speeds):.6f} m/s")
        
        return True
        
    except Exception as e:
        print(f"绘图失败: {e}")
        return False

def analyze_collision():
    """分析碰撞结果"""
    try:
        df = pd.read_excel('result2.xlsx')
        print(f"\n=== 碰撞分析 ===")
        
        collision_time = df['time'].iloc[0]
        print(f"碰撞时刻: {collision_time} s")
        
        # 龙头位置
        x_head = df['handle_1_x'].iloc[0]
        y_head = df['handle_1_y'].iloc[0]
        distance = math.sqrt(x_head**2 + y_head**2)
        
        print(f"碰撞时龙头位置: ({x_head:.6f}, {y_head:.6f})")
        print(f"龙头到中心距离: {distance:.6f} m")
        
        return True
        
    except Exception as e:
        print(f"碰撞分析失败: {e}")
        return False

def plot_dragon_shape():
    """绘制特定时刻的龙队形状"""
    try:
        df = pd.read_excel('result1.xlsx')
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        # 选择几个关键时刻
        times = [0, 120, 240]
        colors = ['red', 'green', 'blue']
        
        for i, t in enumerate(times):
            if t <= df['time'].max():
                row = df[df['time'] == t].iloc[0]
                
                # 提取所有把手位置
                x_positions = []
                y_positions = []
                
                for j in range(1, 225):  # 224个把手
                    if f'handle_{j}_x' in row:
                        x_positions.append(row[f'handle_{j}_x'])
                        y_positions.append(row[f'handle_{j}_y'])
                
                # 绘制龙队
                if len(x_positions) > 0:
                    ax.plot(x_positions, y_positions, 'o-', 
                           color=colors[i], markersize=1, linewidth=1, 
                           label=f't={t}s', alpha=0.7)
                    
                    # 标记龙头
                    ax.plot(x_positions[0], y_positions[0], 'o', 
                           color=colors[i], markersize=6)
        
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_title('不同时刻的龙队形状')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        plt.tight_layout()
        plt.savefig('dragon_shapes.png', dpi=300, bbox_inches='tight')
        print("龙队形状图已保存为 dragon_shapes.png")
        
        return True
        
    except Exception as e:
        print(f"龙队形状绘制失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 板凳龙结果可视化 ===")
    
    # 基本轨迹分析
    if plot_basic_trajectory():
        print("✓ 基本轨迹分析完成")
    
    # 碰撞分析
    if analyze_collision():
        print("✓ 碰撞分析完成")
    
    # 龙队形状
    if plot_dragon_shape():
        print("✓ 龙队形状分析完成")
    
    print("\n生成的图片文件:")
    print("- basic_analysis.png: 轨迹和速度分析")
    print("- dragon_shapes.png: 不同时刻龙队形状")

if __name__ == "__main__":
    main()
