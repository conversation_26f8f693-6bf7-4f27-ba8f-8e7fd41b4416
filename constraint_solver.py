"""
约束求解器
用于求解板凳龙各把手的位置，满足长度约束
"""

import numpy as np
import math
from typing import List, Tuple, Optional
from scipy.optimize import fsolve, minimize_scalar
import warnings
warnings.filterwarnings('ignore')

class ConstraintSolver:
    """约束求解器类"""
    
    def __init__(self):
        self.positions_history = []  # 存储历史位置，用于保持连续性
        self.velocities_history = []  # 存储历史速度
    
    def solve_dragon_positions(self, head_pos: Tuple[float, float], 
                             head_vel: Tuple[float, float],
                             connection_lengths: List[float],
                             dt: float = 1.0) -> Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]:
        """
        求解整个龙队的位置和速度
        
        Args:
            head_pos: 龙头前把手位置
            head_vel: 龙头前把手速度
            connection_lengths: 各段连接长度
            dt: 时间步长
            
        Returns:
            (positions, velocities): 所有把手的位置和速度列表
        """
        positions = [head_pos]
        velocities = [head_vel]
        
        # 如果有历史数据，使用历史数据作为初始猜测
        if len(self.positions_history) > 0:
            prev_positions = self.positions_history[-1]
            prev_velocities = self.velocities_history[-1]
        else:
            prev_positions = None
            prev_velocities = None
        
        current_pos = head_pos
        current_vel = head_vel
        
        for i, length in enumerate(connection_lengths):
            # 获取前一时刻的位置作为初始猜测
            if prev_positions and i + 1 < len(prev_positions):
                initial_guess = prev_positions[i + 1]
                prev_vel_guess = prev_velocities[i + 1] if prev_velocities else (0, 0)
            else:
                # 如果没有历史数据，使用简单的方向估计
                if i == 0:
                    # 第一个连接，使用龙头速度方向
                    direction = math.atan2(current_vel[1], current_vel[0])
                else:
                    # 后续连接，使用前一个连接的方向
                    prev_direction = math.atan2(positions[i][1] - positions[i-1][1], 
                                              positions[i][0] - positions[i-1][0])
                    direction = prev_direction
                
                initial_guess = (current_pos[0] + length * math.cos(direction),
                               current_pos[1] + length * math.sin(direction))
                prev_vel_guess = (0, 0)
            
            # 求解下一个把手的位置
            next_pos = self._solve_single_constraint(current_pos, length, initial_guess)
            
            # 计算速度（通过位置差分）
            if prev_positions and i + 1 < len(prev_positions):
                prev_pos = prev_positions[i + 1]
                next_vel = ((next_pos[0] - prev_pos[0]) / dt,
                           (next_pos[1] - prev_pos[1]) / dt)
            else:
                # 如果没有历史数据，使用约束微分计算速度
                next_vel = self._calculate_constrained_velocity(current_pos, current_vel, 
                                                              next_pos, length)
            
            positions.append(next_pos)
            velocities.append(next_vel)
            
            current_pos = next_pos
            current_vel = next_vel
        
        # 添加龙尾后把手
        tail_length = connection_lengths[-1] if connection_lengths else 1.65  # 默认龙尾长度
        if prev_positions and len(prev_positions) > len(positions):
            tail_initial = prev_positions[len(positions)]
        else:
            direction = math.atan2(current_vel[1], current_vel[0])
            tail_initial = (current_pos[0] + tail_length * math.cos(direction),
                           current_pos[1] + tail_length * math.sin(direction))
        
        tail_pos = self._solve_single_constraint(current_pos, tail_length, tail_initial)
        
        # 龙尾速度
        if prev_positions and len(prev_positions) > len(positions):
            prev_tail_pos = prev_positions[len(positions)]
            tail_vel = ((tail_pos[0] - prev_tail_pos[0]) / dt,
                       (tail_pos[1] - prev_tail_pos[1]) / dt)
        else:
            tail_vel = self._calculate_constrained_velocity(current_pos, current_vel, 
                                                          tail_pos, tail_length)
        
        positions.append(tail_pos)
        velocities.append(tail_vel)
        
        # 保存历史数据
        self.positions_history.append(positions.copy())
        self.velocities_history.append(velocities.copy())
        
        # 只保留最近几步的历史数据
        if len(self.positions_history) > 10:
            self.positions_history.pop(0)
            self.velocities_history.pop(0)
        
        return positions, velocities
    
    def _solve_single_constraint(self, anchor_pos: Tuple[float, float], 
                               constraint_length: float,
                               initial_guess: Tuple[float, float]) -> Tuple[float, float]:
        """
        求解单个约束位置
        给定锚点位置和约束长度，求解目标位置
        """
        x0, y0 = anchor_pos
        x_guess, y_guess = initial_guess
        
        def constraint_equation(vars):
            x, y = vars
            return [(x - x0)**2 + (y - y0)**2 - constraint_length**2]
        
        # 使用数值求解
        try:
            # 方法1：使用fsolve求解约束方程
            solution = fsolve(lambda vars: constraint_equation(vars)[0], 
                            [x_guess, y_guess])
            
            # 验证解的有效性
            x_sol, y_sol = solution
            error = abs((x_sol - x0)**2 + (y_sol - y0)**2 - constraint_length**2)
            
            if error < 1e-6:
                return (x_sol, y_sol)
            else:
                # 如果fsolve失败，使用几何方法
                return self._geometric_constraint_solve(anchor_pos, constraint_length, initial_guess)
                
        except:
            # 如果数值求解失败，使用几何方法
            return self._geometric_constraint_solve(anchor_pos, constraint_length, initial_guess)
    
    def _geometric_constraint_solve(self, anchor_pos: Tuple[float, float], 
                                  constraint_length: float,
                                  initial_guess: Tuple[float, float]) -> Tuple[float, float]:
        """
        使用几何方法求解约束位置
        在以anchor_pos为圆心、constraint_length为半径的圆上找到最接近initial_guess的点
        """
        x0, y0 = anchor_pos
        x_guess, y_guess = initial_guess
        
        # 计算从锚点到猜测点的方向
        dx = x_guess - x0
        dy = y_guess - y0
        dist = math.sqrt(dx**2 + dy**2)
        
        if dist < 1e-10:
            # 如果猜测点与锚点重合，随机选择一个方向
            angle = 0
        else:
            angle = math.atan2(dy, dx)
        
        # 在圆上的对应点
        x_sol = x0 + constraint_length * math.cos(angle)
        y_sol = y0 + constraint_length * math.sin(angle)
        
        return (x_sol, y_sol)
    
    def _calculate_constrained_velocity(self, anchor_pos: Tuple[float, float],
                                      anchor_vel: Tuple[float, float],
                                      target_pos: Tuple[float, float],
                                      constraint_length: float) -> Tuple[float, float]:
        """
        计算满足约束的速度
        使用约束微分方法
        """
        x0, y0 = anchor_pos
        vx0, vy0 = anchor_vel
        x1, y1 = target_pos
        
        # 约束：(x1-x0)^2 + (y1-y0)^2 = L^2
        # 微分：2(x1-x0)(vx1-vx0) + 2(y1-y0)(vy1-vy0) = 0
        # 即：(x1-x0)(vx1-vx0) + (y1-y0)(vy1-vy0) = 0
        
        dx = x1 - x0
        dy = y1 - y0
        
        # 如果距离太小，返回零速度
        if abs(dx) < 1e-10 and abs(dy) < 1e-10:
            return (0, 0)
        
        # 约束方程：dx*(vx1-vx0) + dy*(vy1-vy0) = 0
        # 这给出了一个约束，我们需要另一个条件来确定唯一解
        # 简化处理：假设目标点的速度方向垂直于连接方向
        
        # 连接向量的单位向量
        length = math.sqrt(dx**2 + dy**2)
        if length > 1e-10:
            unit_dx = dx / length
            unit_dy = dy / length
            
            # 垂直方向的单位向量
            perp_dx = -unit_dy
            perp_dy = unit_dx
            
            # 锚点速度在连接方向上的分量
            anchor_radial_vel = vx0 * unit_dx + vy0 * unit_dy
            
            # 目标点在连接方向上的速度分量必须等于锚点的
            target_radial_vel = anchor_radial_vel
            
            # 假设目标点在垂直方向上的速度分量为锚点垂直分量的某个比例
            anchor_perp_vel = vx0 * perp_dx + vy0 * perp_dy
            target_perp_vel = anchor_perp_vel * 0.9  # 稍微衰减
            
            # 合成目标速度
            vx1 = target_radial_vel * unit_dx + target_perp_vel * perp_dx
            vy1 = target_radial_vel * unit_dy + target_perp_vel * perp_dy
            
            return (vx1, vy1)
        else:
            return (0, 0)
