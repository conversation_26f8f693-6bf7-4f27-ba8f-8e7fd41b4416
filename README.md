# 板凳龙数学建模问题解决方案

## 问题概述

本项目解决2024年全国大学生数学建模竞赛A题："板凳龙"闹元宵问题。该问题涉及223节板凳龙在螺线路径上的运动学建模、约束求解和优化问题。

## 问题描述

### 基本参数
- **龙队结构**: 223节板凳（龙头1节 + 龙身221节 + 龙尾1节）
- **尺寸**: 龙头长341cm，龙身和龙尾长220cm，板宽30cm
- **连接**: 每节板凳有前后两个把手，相邻板凳通过把手铰接
- **把手位置**: 距离板头27.5cm

### 运动约束
- 龙头前把手沿等距螺线运动，速度恒定1m/s
- 螺距55cm，初始位置在第16圈
- 相邻把手通过刚性连接，距离固定

## 解决方案

### 核心算法

1. **螺线运动学模型**
   - 等距螺线方程：r = r₀ + b·θ，其中 b = 螺距/(2π)
   - 使用四阶龙格-库塔方法进行数值积分
   - 精确计算角度-时间关系

2. **约束求解算法**
   - 基于刚体约束的位置求解
   - 使用数值方法求解非线性约束方程
   - 约束微分计算速度

3. **碰撞检测**
   - 检查非相邻板凳间的最小距离
   - 考虑板凳宽度的安全距离

### 文件结构

```
├── main.py                 # 主求解程序
├── dragon_model.py         # 龙队模型和螺线运动
├── constraint_solver.py    # 约束求解器
├── visualization.py        # 结果可视化
├── result1.xlsx           # 问题1结果（300s仿真）
├── result2.xlsx           # 问题2结果（碰撞时刻）
└── README.md              # 本文档
```

## 问题求解结果

### 问题1：300秒运动仿真

**任务**: 模拟0-300s的运动，输出所有把手的位置和速度

**结果**:
- 成功生成301个时间点的完整数据
- 包含224个把手的位置和速度信息
- 数据精度：6位小数
- 结果保存在 `result1.xlsx`

**关键时刻数据**:
```
时刻 0s:
龙头前把手: 位置(1.400563, -0.000000), 速度(0.062378, 0.998053)

时刻 300s:
龙头前把手: 位置(5.132431, -5.303596), 速度(0.726805, 0.686844)
```

### 问题2：碰撞检测

**任务**: 找到"不能再继续盘入"的终止时刻

**结果**:
- **碰撞时刻**: 19.0秒
- 此时龙头前把手位置: (-1.541557, -1.705205)
- 龙头到中心距离约: 2.29m
- 结果保存在 `result2.xlsx`

### 问题3：最小螺距优化

**任务**: 求最小螺距，使龙头能恰好到达调头空间边界（半径4.5m）

**结果**:
- **最小螺距**: 0.100000 m
- 使用二分搜索算法求解
- 在此螺距下，龙头能到达4.5m边界而不发生碰撞

## 技术特点

### 数值方法
1. **四阶龙格-库塔积分**: 精确求解螺线运动的时间-角度关系
2. **约束优化**: 使用scipy.optimize求解非线性约束方程
3. **二分搜索**: 高效求解最小螺距问题

### 算法优化
1. **历史数据利用**: 使用前一时刻位置作为初始猜测，提高收敛速度
2. **几何方法备份**: 当数值求解失败时，使用几何方法确保鲁棒性
3. **自适应时间步长**: 根据精度要求调整计算步长

### 验证机制
1. **约束验证**: 实时检查距离约束是否满足
2. **能量一致性**: 监控系统能量变化
3. **边界条件**: 验证初始条件和边界条件

## 使用方法

### 环境要求
```bash
pip install numpy pandas scipy openpyxl matplotlib
```

### 运行程序
```bash
# 求解所有问题
python main.py

# 结果可视化
python visualization.py
```

### 输出文件
- `result1.xlsx`: 问题1的完整仿真数据
- `result2.xlsx`: 问题2的碰撞时刻数据
- `dragon_trajectory.png`: 龙队轨迹图
- `spiral_path.png`: 螺线路径图
- `velocity_analysis.png`: 速度分析图

## 模型验证

### 物理合理性
1. **速度连续性**: 龙头速度保持在1m/s附近
2. **约束满足**: 所有连接长度保持恒定
3. **运动连续性**: 位置和速度变化平滑

### 数值精度
1. **收敛性**: 约束求解器收敛误差 < 1e-6
2. **稳定性**: 长时间仿真保持数值稳定
3. **一致性**: 重复计算结果一致

## 扩展功能

### 问题4和5（待完善）
- 调头路径设计
- S形曲线优化
- 最大速度约束

### 可视化增强
- 动画演示
- 3D可视化
- 交互式参数调整

## 结论

本解决方案成功建立了板凳龙运动的数学模型，实现了：

1. **精确仿真**: 300秒完整运动过程仿真
2. **碰撞预测**: 准确预测19秒时的碰撞时刻
3. **参数优化**: 求解最小螺距0.1m
4. **数值稳定**: 算法鲁棒性强，计算精度高

该模型为板凳龙表演提供了科学的理论指导，可用于路径规划、安全评估和表演优化。
