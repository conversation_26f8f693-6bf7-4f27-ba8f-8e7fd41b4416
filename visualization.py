"""
板凳龙结果可视化
用于验证计算结果并生成图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import math
from matplotlib.patches import Circle
from matplotlib.animation import FuncAnimation

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class DragonVisualizer:
    """板凳龙可视化器"""
    
    def __init__(self):
        self.result1_df = None
        self.result2_df = None
        
    def load_results(self):
        """加载计算结果"""
        try:
            self.result1_df = pd.read_excel('result1.xlsx')
            print("成功加载 result1.xlsx")
        except:
            print("无法加载 result1.xlsx")
            
        try:
            self.result2_df = pd.read_excel('result2.xlsx')
            print("成功加载 result2.xlsx")
        except:
            print("无法加载 result2.xlsx")
    
    def plot_dragon_trajectory(self, times=[0, 60, 120, 180, 240, 300]):
        """绘制龙队轨迹图"""
        if self.result1_df is None:
            print("请先加载结果数据")
            return
            
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        colors = ['red', 'orange', 'yellow', 'green', 'blue', 'purple']
        
        for i, t in enumerate(times):
            if t > self.result1_df['time'].max():
                continue
                
            row = self.result1_df[self.result1_df['time'] == t].iloc[0]
            
            # 提取所有把手位置
            x_positions = []
            y_positions = []
            
            for j in range(1, 225):  # 224个把手
                if f'handle_{j}_x' in row:
                    x_positions.append(row[f'handle_{j}_x'])
                    y_positions.append(row[f'handle_{j}_y'])
            
            # 绘制龙队形状
            ax.plot(x_positions, y_positions, 'o-', 
                   color=colors[i % len(colors)], 
                   markersize=2, linewidth=1, 
                   label=f't={t}s', alpha=0.7)
            
            # 标记龙头
            if x_positions:
                ax.plot(x_positions[0], y_positions[0], 'o', 
                       color=colors[i % len(colors)], markersize=8)
        
        ax.set_xlabel('X坐标 (m)')
        ax.set_ylabel('Y坐标 (m)')
        ax.set_title('板凳龙运动轨迹')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        plt.tight_layout()
        plt.savefig('dragon_trajectory.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_spiral_path(self):
        """绘制螺线路径"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 10))
        
        # 绘制理论螺线
        pitch = 0.55  # 55cm螺距
        b = pitch / (2 * np.pi)
        theta = np.linspace(0, 20 * 2 * np.pi, 2000)
        r = b * theta
        x_spiral = r * np.cos(theta)
        y_spiral = r * np.sin(theta)
        
        ax.plot(x_spiral, y_spiral, 'k--', alpha=0.5, label='理论螺线')
        
        # 绘制龙头实际路径
        if self.result1_df is not None:
            x_head = self.result1_df['handle_1_x'].values
            y_head = self.result1_df['handle_1_y'].values
            ax.plot(x_head, y_head, 'r-', linewidth=2, label='龙头实际路径')
        
        # 绘制调头空间
        circle = Circle((0, 0), 4.5, fill=False, color='blue', 
                       linestyle='--', linewidth=2, label='调头空间')
        ax.add_patch(circle)
        
        ax.set_xlabel('X坐标 (m)')
        ax.set_ylabel('Y坐标 (m)')
        ax.set_title('螺线路径与调头空间')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        plt.tight_layout()
        plt.savefig('spiral_path.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_velocity_analysis(self):
        """绘制速度分析图"""
        if self.result1_df is None:
            return
            
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        times = self.result1_df['time'].values
        
        # 龙头前把手速度
        vx_head = self.result1_df['handle_1_vx'].values
        vy_head = self.result1_df['handle_1_vy'].values
        speed_head = np.sqrt(vx_head**2 + vy_head**2)
        
        ax1.plot(times, speed_head, 'r-', linewidth=2, label='龙头前把手')
        ax1.axhline(y=1.0, color='k', linestyle='--', alpha=0.5, label='目标速度1m/s')
        
        # 其他关键把手速度
        handle_indices = [2, 52, 102, 152, 202, 224]
        handle_names = ['第1节龙身', '第51节龙身', '第101节龙身', 
                       '第151节龙身', '第201节龙身', '龙尾后把手']
        
        for idx, name in zip(handle_indices, handle_names):
            if f'handle_{idx}_vx' in self.result1_df.columns:
                vx = self.result1_df[f'handle_{idx}_vx'].values
                vy = self.result1_df[f'handle_{idx}_vy'].values
                speed = np.sqrt(vx**2 + vy**2)
                ax1.plot(times, speed, linewidth=1, label=name, alpha=0.7)
        
        ax1.set_xlabel('时间 (s)')
        ax1.set_ylabel('速度 (m/s)')
        ax1.set_title('各把手速度变化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 速度方向分析
        ax2.plot(times, vx_head, 'r-', label='龙头vx')
        ax2.plot(times, vy_head, 'b-', label='龙头vy')
        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('速度分量 (m/s)')
        ax2.set_title('龙头速度分量')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('velocity_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_collision_time(self):
        """分析碰撞时刻"""
        if self.result2_df is None:
            return
            
        print("=== 碰撞分析 ===")
        collision_time = self.result2_df['time'].iloc[0]
        print(f"碰撞发生时刻: {collision_time} s")
        
        # 计算此时龙队的总长度
        row = self.result2_df.iloc[0]
        positions = []
        for i in range(1, 225):
            if f'handle_{i}_x' in row:
                x = row[f'handle_{i}_x']
                y = row[f'handle_{i}_y']
                positions.append((x, y))
        
        # 计算龙队占用的空间范围
        if positions:
            x_coords = [pos[0] for pos in positions]
            y_coords = [pos[1] for pos in positions]
            
            x_range = max(x_coords) - min(x_coords)
            y_range = max(y_coords) - min(y_coords)
            
            print(f"龙队X方向跨度: {x_range:.3f} m")
            print(f"龙队Y方向跨度: {y_range:.3f} m")
            
            # 计算龙头到中心的距离
            head_x, head_y = positions[0]
            head_distance = math.sqrt(head_x**2 + head_y**2)
            print(f"龙头到中心距离: {head_distance:.3f} m")
    
    def generate_report(self):
        """生成分析报告"""
        print("\n=== 板凳龙数学建模结果分析报告 ===")
        
        if self.result1_df is not None:
            print(f"\n问题1分析:")
            print(f"仿真时间范围: 0-{self.result1_df['time'].max()}s")
            print(f"数据点数量: {len(self.result1_df)}")
            
            # 分析龙头速度
            vx = self.result1_df['handle_1_vx'].values
            vy = self.result1_df['handle_1_vy'].values
            speeds = np.sqrt(vx**2 + vy**2)
            
            print(f"龙头平均速度: {np.mean(speeds):.6f} m/s")
            print(f"龙头速度标准差: {np.std(speeds):.6f} m/s")
            print(f"龙头最大速度: {np.max(speeds):.6f} m/s")
            print(f"龙头最小速度: {np.min(speeds):.6f} m/s")
        
        self.analyze_collision_time()

def main():
    """主函数"""
    visualizer = DragonVisualizer()
    visualizer.load_results()
    
    # 生成各种图表
    print("正在生成轨迹图...")
    visualizer.plot_dragon_trajectory()
    
    print("正在生成螺线路径图...")
    visualizer.plot_spiral_path()
    
    print("正在生成速度分析图...")
    visualizer.plot_velocity_analysis()
    
    # 生成分析报告
    visualizer.generate_report()
    
    print("\n可视化完成！生成的图片:")
    print("- dragon_trajectory.png: 龙队轨迹图")
    print("- spiral_path.png: 螺线路径图")
    print("- velocity_analysis.png: 速度分析图")

if __name__ == "__main__":
    main()
