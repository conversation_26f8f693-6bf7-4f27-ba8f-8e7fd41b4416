"""
板凳龙问题求解主程序
解决2024年数学建模竞赛A题的5个问题
"""

import numpy as np
import pandas as pd
import math
from typing import List, Tuple
from dragon_model import DragonModel, SpiralMotion
from constraint_solver import ConstraintSolver

class DragonSolver:
    """板凳龙问题求解器"""

    def __init__(self):
        self.dragon = DragonModel()
        self.spiral = None
        self.constraint_solver = ConstraintSolver()
        
    def solve_problem1(self) -> pd.DataFrame:
        """
        问题1：模拟0-300s的运动，输出所有把手的位置和速度
        """
        print("正在求解问题1...")
        
        # 初始化螺线运动（螺距55cm，初始在第16圈）
        pitch = 0.55  # 55cm = 0.55m
        initial_angle = 16 * 2 * math.pi  # 第16圈
        initial_radius = pitch * 16 / (2 * math.pi)  # 对应的初始半径
        
        self.spiral = SpiralMotion(pitch, initial_radius, initial_angle)
        
        # 时间范围：0-300s，每秒记录一次
        times = range(301)  # 0, 1, 2, ..., 300
        results = []
        
        for t in times:
            positions, velocities = self._calculate_dragon_state(t)
            
            # 记录结果
            row = {'time': t}
            for i, (pos, vel) in enumerate(zip(positions, velocities)):
                row[f'handle_{i+1}_x'] = round(pos[0], 6)
                row[f'handle_{i+1}_y'] = round(pos[1], 6)
                row[f'handle_{i+1}_vx'] = round(vel[0], 6)
                row[f'handle_{i+1}_vy'] = round(vel[1], 6)
            
            results.append(row)
            
            if t % 60 == 0:  # 每60秒输出一次进度
                print(f"已完成时间: {t}s")
        
        df = pd.DataFrame(results)
        df.to_excel('result1.xlsx', index=False)
        print("问题1求解完成，结果保存到 result1.xlsx")
        
        # 输出论文需要的特定时刻数据
        self._print_specific_handles(df, [0, 60, 120, 180, 240, 300])
        
        return df
    
    def solve_problem2(self) -> pd.DataFrame:
        """
        问题2：找到不能再继续盘入的终止时刻
        """
        print("正在求解问题2...")

        # 使用问题1的设置
        if self.spiral is None:
            pitch = 0.55
            initial_angle = 16 * 2 * math.pi
            initial_radius = pitch * 16 / (2 * math.pi)
            self.spiral = SpiralMotion(pitch, initial_radius, initial_angle)

        # 从0开始，逐步增加时间，直到检测到碰撞
        t = 0
        dt = 1.0  # 1秒步长
        max_time = 1000  # 最大搜索时间

        while t < max_time:
            positions, velocities = self._calculate_dragon_state(t)

            # 检查是否发生碰撞
            if self._check_collision(positions):
                print(f"检测到碰撞，终止时刻: {t}s")
                break

            t += dt

            if t % 60 == 0:
                print(f"检查时间: {t}s")

        # 保存终止时刻的结果
        positions, velocities = self._calculate_dragon_state(t)
        results = []

        row = {'time': t}
        for i, (pos, vel) in enumerate(zip(positions, velocities)):
            row[f'handle_{i+1}_x'] = round(pos[0], 6)
            row[f'handle_{i+1}_y'] = round(pos[1], 6)
            row[f'handle_{i+1}_vx'] = round(vel[0], 6)
            row[f'handle_{i+1}_vy'] = round(vel[1], 6)

        results.append(row)

        df = pd.DataFrame(results)
        df.to_excel('result2.xlsx', index=False)
        print("问题2求解完成，结果保存到 result2.xlsx")

        # 输出论文需要的特定把手数据
        self._print_specific_handles(df, [t])

        return df

    def solve_problem3(self) -> float:
        """
        问题3：求最小螺距，使龙头前把手能恰好到达调头空间边界
        调头空间：半径4.5m的圆
        """
        print("正在求解问题3...")

        target_radius = 4.5  # 调头空间半径

        # 使用二分搜索找到最小螺距
        pitch_min = 0.1  # 最小螺距
        pitch_max = 2.0  # 最大螺距
        tolerance = 1e-6  # 精度要求

        while pitch_max - pitch_min > tolerance:
            pitch_mid = (pitch_min + pitch_max) / 2

            # 测试这个螺距能否到达目标半径
            if self._can_reach_radius(pitch_mid, target_radius):
                pitch_max = pitch_mid
            else:
                pitch_min = pitch_mid

        optimal_pitch = (pitch_min + pitch_max) / 2
        print(f"问题3求解完成，最小螺距: {optimal_pitch:.6f} m")

        return optimal_pitch

    def _can_reach_radius(self, pitch: float, target_radius: float) -> bool:
        """
        测试给定螺距是否能让龙头到达目标半径而不发生碰撞
        """
        # 重新初始化螺线
        initial_angle = 16 * 2 * math.pi
        initial_radius = pitch * 16 / (2 * math.pi)
        test_spiral = SpiralMotion(pitch, initial_radius, initial_angle)

        # 创建临时约束求解器
        temp_solver = ConstraintSolver()

        # 模拟运动直到到达目标半径
        t = 0
        dt = 0.5  # 较小的时间步长
        max_time = 500

        while t < max_time:
            theta = test_spiral.theta_from_time(t)
            head_pos = test_spiral.position(theta)
            head_vel = test_spiral.velocity(theta)

            # 检查是否到达目标半径
            current_radius = math.sqrt(head_pos[0]**2 + head_pos[1]**2)
            if current_radius <= target_radius:
                return True

            # 计算整个龙队状态并检查碰撞
            connection_lengths = self.dragon.get_connection_lengths()
            positions, _ = temp_solver.solve_dragon_positions(
                head_pos, head_vel, connection_lengths, dt)

            if self._check_collision(positions):
                return False

            t += dt

        return False
    
    def _calculate_dragon_state(self, t: float) -> Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]:
        """
        计算给定时间t时整个龙队的状态
        返回：(位置列表, 速度列表)
        """
        # 计算龙头前把手的位置和速度
        theta = self.spiral.theta_from_time(t)
        head_pos = self.spiral.position(theta)
        head_vel = self.spiral.velocity(theta)

        # 获取连接长度
        connection_lengths = self.dragon.get_connection_lengths()

        # 使用约束求解器计算所有把手的位置和速度
        positions, velocities = self.constraint_solver.solve_dragon_positions(
            head_pos, head_vel, connection_lengths, dt=1.0)

        return positions, velocities
    
    def _check_collision(self, positions: List[Tuple[float, float]]) -> bool:
        """
        检查是否发生碰撞
        简化处理：检查任意两个不相邻把手之间的距离
        """
        min_distance = 0.3  # 最小安全距离 (30cm板宽)
        
        for i in range(len(positions)):
            for j in range(i + 3, len(positions)):  # 跳过相邻的把手
                dist = math.sqrt((positions[i][0] - positions[j][0])**2 + 
                               (positions[i][1] - positions[j][1])**2)
                if dist < min_distance:
                    return True
        
        return False
    
    def _print_specific_handles(self, df: pd.DataFrame, times: List[float]):
        """
        输出论文需要的特定把手在特定时刻的位置和速度
        """
        # 需要输出的把手：龙头前把手，龙头后第1,51,101,151,201节龙身前把手，龙尾后把手
        handle_indices = [1, 2, 52, 102, 152, 202, 224]  # 对应的把手编号
        handle_names = ['龙头前把手', '第1节龙身前把手', '第51节龙身前把手', 
                       '第101节龙身前把手', '第151节龙身前把手', '第201节龙身前把手', '龙尾后把手']
        
        print("\n=== 论文所需数据 ===")
        for t in times:
            print(f"\n时刻 {t}s:")
            row = df[df['time'] == t].iloc[0]
            
            for idx, name in zip(handle_indices, handle_names):
                x = row[f'handle_{idx}_x']
                y = row[f'handle_{idx}_y']
                vx = row[f'handle_{idx}_vx']
                vy = row[f'handle_{idx}_vy']
                print(f"{name}: 位置({x:.6f}, {y:.6f}), 速度({vx:.6f}, {vy:.6f})")

def main():
    """主函数"""
    solver = DragonSolver()

    print("=== 板凳龙数学建模问题求解 ===\n")

    # 求解问题1
    print("开始求解问题1...")
    solver.solve_problem1()

    # 求解问题2
    print("\n开始求解问题2...")
    solver.solve_problem2()

    # 求解问题3
    print("\n开始求解问题3...")
    optimal_pitch = solver.solve_problem3()

    print(f"\n=== 求解结果汇总 ===")
    print(f"问题1: 300s运动仿真完成，结果保存到 result1.xlsx")
    print(f"问题2: 碰撞检测完成，结果保存到 result2.xlsx")
    print(f"问题3: 最小螺距 = {optimal_pitch:.6f} m")
    print(f"\n所有问题求解完成！")

if __name__ == "__main__":
    main()
